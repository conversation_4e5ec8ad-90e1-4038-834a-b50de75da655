#!/usr/bin/env python3
"""
Convert video frame library to a single 2D array [230][16384]
"""

import re
import sys

def convert_to_single_array():
    """Convert all frames to a single 2D array format"""
    
    # Read the original file
    with open('Driver/dongtu3.h', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Start building the new content
    new_content = []
    new_content.append('#ifndef USER_PIC_H_')
    new_content.append('#define USER_PIC_H_')
    new_content.append('')
    new_content.append('// 128x128 video frame library as single 2D array')
    new_content.append('// Format: dongtu3_frames[frame_index][pixel_index]')
    new_content.append('// Total: 229 frames × 16384 pixels per frame')
    new_content.append('// Frame indices: 0-228 (corresponding to original frames 1-229)')
    new_content.append('')
    new_content.append('const uint16_t dongtu3_frames[229][16384] = {')
    
    # Find all frame definitions
    frame_pattern = r'const uint16_t dongtu3_(\d+)\[\](?:\s+PROGMEM)?\s*=\s*\{(.*?)\};'
    frames = re.findall(frame_pattern, content, re.DOTALL)
    
    print(f"Found {len(frames)} frames to convert")
    
    # Sort frames by number to ensure correct order
    frames_dict = {}
    for frame_num, frame_data in frames:
        # Extract all hex values from the frame data
        hex_values = re.findall(r'0x[0-9A-Fa-f]+', frame_data)
        
        if len(hex_values) != 16384:  # 128 * 128
            print(f"Warning: Frame {frame_num} has {len(hex_values)} values instead of 16384")
            continue
        
        frames_dict[int(frame_num)] = hex_values
    
    # Convert frames in order (1-229, note: frame 0 might not exist)
    # Map to array indices 0-228 (229 frames total)
    frame_count = 0
    for array_index in range(229):  # 0 to 228 (229 frames)
        frame_num = array_index + 1  # Original frame numbers are 1-229
        if frame_num in frames_dict:
            print(f"Converting frame {frame_num} to array index {array_index}...")

            # Add frame data as a single row
            frame_data = frames_dict[frame_num]

            # Format the frame data (split into multiple lines for readability)
            new_content.append(f'    // Frame {frame_num} (array index {array_index})')
            new_content.append('    {')

            # Split into lines of 16 values each for better readability
            for i in range(0, 16384, 16):
                line_values = frame_data[i:i+16]
                line_str = '        ' + ', '.join(line_values)
                if i + 16 < 16384:  # Not the last line of this frame
                    line_str += ','
                new_content.append(line_str)

            new_content.append('    }' + (',' if array_index < 228 else ''))
            frame_count += 1
        else:
            print(f"Warning: Frame {frame_num} not found, filling with zeros...")
            # Fill missing frame with zeros
            new_content.append(f'    // Frame {frame_num} (array index {array_index}) - MISSING, filled with zeros')
            new_content.append('    {')

            # Fill with zeros
            for i in range(0, 16384, 16):
                if i == 0:
                    line_str = '        ' + ', '.join(['0x0000'] * 16)
                else:
                    line_str = '        ' + ', '.join(['0x0000'] * 16)
                if i + 16 < 16384:
                    line_str += ','
                new_content.append(line_str)

            new_content.append('    }' + (',' if array_index < 228 else ''))
            frame_count += 1
    
    # Close the array and file
    new_content.append('};')
    new_content.append('')
    new_content.append('#endif')
    
    # Write the new file
    with open('Driver/dongtu3_single_array.h', 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_content))
    
    print(f"Conversion complete! New file saved as Driver/dongtu3_single_array.h")
    print(f"Converted {frame_count} frames to single 2D array format [229][16384]")
    print(f"Usage: dongtu3_frames[frame_index][pixel_index]")
    print(f"Example: dongtu3_frames[0][0] = first pixel of frame 1")
    print(f"Note: Array indices 0-228 correspond to original frames 1-229")

if __name__ == '__main__':
    convert_to_single_array()
