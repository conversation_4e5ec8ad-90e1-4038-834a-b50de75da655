#!/usr/bin/env python3
"""
分析pic.h文件结构的脚本
用于确定每帧数据的确切格式、大小和布局
"""

import re
import os

def analyze_pic_structure(file_path):
    """分析pic.h文件的结构"""
    
    print("开始分析pic.h文件结构...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有帧定义
    frame_pattern = r'const uint16_t dongtu3_(\d+)\[\]\s*=\s*\{'
    frames = re.findall(frame_pattern, content)

    print(f"找到 {len(frames)} 帧数据")
    if frames:
        print(f"帧编号范围: {min(frames)} - {max(frames)}")
    else:
        print("未找到任何帧数据，尝试其他模式...")
        # 尝试更宽松的模式
        frame_pattern2 = r'dongtu3_(\d+)\[\]'
        frames = re.findall(frame_pattern2, content)
        print(f"使用宽松模式找到 {len(frames)} 帧")
        if frames:
            print(f"帧编号范围: {min(frames)} - {max(frames)}")
    
    # 分析第一帧的详细结构
    first_frame_pattern = r'const uint16_t dongtu3_0\[\]\s*=\s*\{(.*?)\};'
    first_frame_match = re.search(first_frame_pattern, content, re.DOTALL)

    if not first_frame_match:
        # 尝试更宽松的模式
        first_frame_pattern = r'dongtu3_0\[\]\s*=\s*\{(.*?)\};'
        first_frame_match = re.search(first_frame_pattern, content, re.DOTALL)
    
    if first_frame_match:
        first_frame_data = first_frame_match.group(1)
        
        # 提取所有十六进制值
        hex_values = re.findall(r'0x[0-9A-Fa-f]+', first_frame_data)
        
        print(f"\n第一帧数据分析:")
        print(f"总像素数: {len(hex_values)}")
        print(f"预期像素数 (128*128): {128*128}")
        print(f"数据完整性: {'✓' if len(hex_values) == 16384 else '✗'}")
        
        # 分析数据排列方式
        if len(hex_values) >= 128:
            print(f"\n前16个像素值: {hex_values[:16]}")
            print(f"第二行前16个像素值: {hex_values[128:144] if len(hex_values) > 144 else '数据不足'}")
        
        # 检查数据类型
        sample_values = hex_values[:10]
        print(f"\n样本像素值: {sample_values}")
        
        # 分析值的范围
        int_values = [int(val, 16) for val in hex_values[:100]]  # 分析前100个值
        print(f"像素值范围: {min(int_values):04X} - {max(int_values):04X}")
        print(f"这表明使用的是16位RGB565格式")
    
    # 查找注释模式
    comment_patterns = [
        r'\d+\)\s*\n// Time generated\s*:\s*[\d\-\s:]+',
        r'// Time generated\s*:\s*[\d\-\s:]+'
    ]
    
    total_comments = 0
    for pattern in comment_patterns:
        matches = re.findall(pattern, content)
        total_comments += len(matches)
        if matches:
            print(f"\n找到注释模式: {pattern}")
            print(f"匹配数量: {len(matches)}")
            print(f"示例: {matches[0][:50]}...")
    
    print(f"\n总注释数量: {total_comments}")
    
    # 分析文件大小
    file_size = len(content)
    print(f"\n文件统计:")
    print(f"文件大小: {file_size:,} 字符")
    print(f"文件大小: {file_size/1024/1024:.2f} MB")
    
    # 估算转换后的大小
    estimated_data_size = len(frames) * 16384 * 8  # 每个像素约8字符 (0xXXXX, )
    estimated_overhead = len(frames) * 200  # 每帧约200字符的声明和格式化
    estimated_total = estimated_data_size + estimated_overhead
    
    print(f"\n转换后预估:")
    print(f"纯数据大小: {estimated_data_size:,} 字符")
    print(f"格式化开销: {estimated_overhead:,} 字符") 
    print(f"预估总大小: {estimated_total:,} 字符 ({estimated_total/1024/1024:.2f} MB)")
    
    return {
        'total_frames': len(frames),
        'frame_range': (min(frames), max(frames)),
        'pixels_per_frame': len(hex_values) if 'hex_values' in locals() else 0,
        'file_size': file_size,
        'comments_found': total_comments
    }

def main():
    file_path = "Driver/pic.h"
    
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return
    
    result = analyze_pic_structure(file_path)
    
    print("\n" + "="*50)
    print("分析完成!")
    print("="*50)
    
    # 验证128*128=16384的计算
    expected_pixels = 128 * 128
    print(f"\n验证计算:")
    print(f"128 × 128 = {expected_pixels}")
    print(f"实际像素数: {result['pixels_per_frame']}")
    print(f"匹配: {'✓' if result['pixels_per_frame'] == expected_pixels else '✗'}")

if __name__ == "__main__":
    main()
