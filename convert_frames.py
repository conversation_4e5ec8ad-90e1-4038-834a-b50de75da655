#!/usr/bin/env python3
"""
Convert video frame library from 1D arrays to 2D arrays
Removes comments and converts 128x128 pixel data to proper 2D array format
"""

import re
import sys

def convert_frame_data():
    """Convert the dongtu3.h file from 1D to 2D array format"""
    
    # Read the original file
    with open('Driver/dongtu3.h', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Start building the new content
    new_content = []
    new_content.append('#ifndef USER_PIC_H_')
    new_content.append('#define USER_PIC_H_')
    new_content.append('')
    new_content.append('// 128x128 video frame library converted to 2D arrays')
    new_content.append('// Total frames: 230 (dongtu3_0 to dongtu3_229)')
    new_content.append('')
    
    # Find all frame definitions
    frame_pattern = r'const uint16_t dongtu3_(\d+)\[\](?:\s+PROGMEM)?\s*=\s*\{(.*?)\};'
    frames = re.findall(frame_pattern, content, re.DOTALL)
    
    print(f"Found {len(frames)} frames to convert")
    
    for i, (frame_num, frame_data) in enumerate(frames):
        print(f"Converting frame {frame_num}...")
        
        # Extract all hex values from the frame data
        hex_values = re.findall(r'0x[0-9A-Fa-f]+', frame_data)
        
        if len(hex_values) != 16384:  # 128 * 128
            print(f"Warning: Frame {frame_num} has {len(hex_values)} values instead of 16384")
            continue
        
        # Add frame declaration
        new_content.append(f'const uint16_t dongtu3_{frame_num}[128][128] = {{')
        
        # Convert to 2D array format (128 rows of 128 values each)
        for row in range(128):
            row_start = row * 128
            row_end = row_start + 128
            row_values = hex_values[row_start:row_end]
            
            # Format the row
            row_str = '    {' + ', '.join(row_values) + '}'
            if row < 127:  # Not the last row
                row_str += ','
            
            new_content.append(row_str)
        
        new_content.append('};')
        new_content.append('')
    
    # Add closing endif
    new_content.append('#endif')
    
    # Write the new file
    with open('Driver/dongtu3_2d.h', 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_content))
    
    print(f"Conversion complete! New file saved as Driver/dongtu3_2d.h")
    print(f"Converted {len(frames)} frames to 2D array format")

if __name__ == '__main__':
    convert_frame_data()
