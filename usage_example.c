/*
 * Usage example for the converted dongtu3_frames array
 * 
 * The array format is: dongtu3_frames[229][16384]
 * - 229 frames (indices 0-228, corresponding to original frames 1-229)
 * - 16384 pixels per frame (128×128 pixels)
 */

#include "Driver/dongtu3_single_array.h"

// Example 1: Access a specific pixel
void example_access_pixel() {
    // Get the first pixel of the first frame (original frame 1)
    uint16_t first_pixel = dongtu3_frames[0][0];
    
    // Get the last pixel of the last frame (original frame 229)
    uint16_t last_pixel = dongtu3_frames[228][16383];
    
    // Get a pixel at row 50, column 75 of frame 10 (original frame 11)
    // Pixel index = row * 128 + column = 50 * 128 + 75 = 6475
    uint16_t specific_pixel = dongtu3_frames[10][6475];
}

// Example 2: Display a frame (pseudo-code for LCD display)
void display_frame(int frame_index) {
    if (frame_index < 0 || frame_index >= 229) {
        return; // Invalid frame index
    }
    
    // Display the frame pixel by pixel
    for (int pixel = 0; pixel < 16384; pixel++) {
        int row = pixel / 128;
        int col = pixel % 128;
        uint16_t color = dongtu3_frames[frame_index][pixel];
        
        // Send pixel to LCD at position (row, col) with color
        // lcd_draw_pixel(col, row, color);
    }
}

// Example 3: Animate through all frames
void animate_frames() {
    for (int frame = 0; frame < 229; frame++) {
        display_frame(frame);
        // delay_ms(100); // 100ms delay between frames
    }
}

// Example 4: Get a specific row of pixels from a frame
void get_frame_row(int frame_index, int row, uint16_t* row_buffer) {
    if (frame_index < 0 || frame_index >= 229 || row < 0 || row >= 128) {
        return; // Invalid parameters
    }
    
    int start_pixel = row * 128;
    for (int col = 0; col < 128; col++) {
        row_buffer[col] = dongtu3_frames[frame_index][start_pixel + col];
    }
}

// Example 5: Copy a frame to a buffer
void copy_frame_to_buffer(int frame_index, uint16_t* buffer) {
    if (frame_index < 0 || frame_index >= 229) {
        return; // Invalid frame index
    }
    
    for (int pixel = 0; pixel < 16384; pixel++) {
        buffer[pixel] = dongtu3_frames[frame_index][pixel];
    }
}

int main() {
    // Example usage
    example_access_pixel();
    display_frame(0);  // Display first frame
    animate_frames();  // Animate all frames
    
    return 0;
}
