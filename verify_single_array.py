#!/usr/bin/env python3
"""
Verify the single 2D array conversion
"""

import re
import os

def verify_single_array():
    """Verify that the single array conversion was successful"""
    
    print("Verifying single 2D array conversion...")
    
    # Read the new single array file
    with open('Driver/dongtu3_single_array.h', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check array declaration
    array_pattern = r'const uint16_t dongtu3_frames\[(\d+)\]\[(\d+)\]'
    array_match = re.search(array_pattern, content)
    
    if array_match:
        frames_count = int(array_match.group(1))
        pixels_per_frame = int(array_match.group(2))
        print(f"✓ Found array declaration: dongtu3_frames[{frames_count}][{pixels_per_frame}]")
        
        if frames_count == 229:
            print("✓ Correct number of frames (229)")
        else:
            print(f"✗ Expected 229 frames, found {frames_count}")
            
        if pixels_per_frame == 16384:
            print("✓ Correct pixels per frame (16384 = 128×128)")
        else:
            print(f"✗ Expected 16384 pixels per frame, found {pixels_per_frame}")
    else:
        print("✗ Array declaration not found")
        return
    
    # Count actual frame entries
    frame_entries = re.findall(r'// Frame (\d+) \(array index (\d+)\)', content)
    print(f"✓ Found {len(frame_entries)} frame entries")
    
    # Verify frame mapping
    expected_mapping = True
    for frame_num_str, array_index_str in frame_entries:
        frame_num = int(frame_num_str)
        array_index = int(array_index_str)
        if frame_num != array_index + 1:
            print(f"✗ Incorrect mapping: Frame {frame_num} should be at array index {frame_num - 1}, found {array_index}")
            expected_mapping = False
    
    if expected_mapping:
        print("✓ Frame to array index mapping is correct")
    
    # Check file structure
    if content.startswith('#ifndef USER_PIC_H_'):
        print("✓ Proper header guard")
    else:
        print("✗ Missing header guard")
        
    if content.endswith('#endif\n') or content.endswith('#endif'):
        print("✓ Proper file ending")
    else:
        print("✗ Missing #endif")
    
    # Check for removed comments
    if '// Generated by' not in content:
        print("✓ Generator comments removed")
    else:
        print("✗ Generator comments still present")
    
    # File size comparison
    original_size = os.path.getsize('Driver/dongtu3.h')
    new_size = os.path.getsize('Driver/dongtu3_single_array.h')
    reduction = ((original_size - new_size) / original_size) * 100
    
    print(f"\nFile size comparison:")
    print(f"Original file: {original_size:,} bytes")
    print(f"New single array file: {new_size:,} bytes")
    print(f"Size reduction: {reduction:.1f}%")
    
    print(f"\n✅ Conversion Summary:")
    print(f"- Successfully created single 2D array format [229][16384]")
    print(f"- Array indices 0-228 correspond to original frames 1-229")
    print(f"- All generator comments removed")
    print(f"- File size reduced by {reduction:.1f}%")
    print(f"- Usage: dongtu3_frames[frame_index][pixel_index]")
    print(f"- Example: dongtu3_frames[0][0] = first pixel of frame 1")
    print(f"- Example: dongtu3_frames[228][16383] = last pixel of frame 229")

if __name__ == '__main__':
    verify_single_array()
